#!/usr/bin/env python3
"""
弹幕聚合服务测试客户端 - DanDanPlay兼容版
"""

import asyncio
import json
from urllib.parse import quote

import httpx

async def test_dandanplay_api():
    """测试DanDanPlay兼容API"""

    base_url = "http://localhost:8080"

    async with httpx.AsyncClient(timeout=30.0) as client:
        print("🚀 开始测试DanDanPlay兼容API...")

        # 1. 测试服务状态
        print("\n📊 检查服务状态...")
        try:
            response = await client.get(f"{base_url}/")
            data = response.json()
            print(f"✅ 服务状态: {data}")
            print(f"   - 番剧数量: {data.get('anime_count', 0)}")
            print(f"   - 分集数量: {data.get('episode_count', 0)}")
        except Exception as e:
            print(f"❌ 服务状态检查失败: {e}")
            return

        # 2. 测试文件匹配
        print("\n📝 测试文件匹配...")
        test_files = [
            "鬼灭之刃.S01E01.1080p.mp4",
            "进击的巨人 第1季 第1集.mkv",
            "One Piece 1001.mp4"
        ]

        episode_ids = []
        for filename in test_files:
            try:
                match_data = {
                    "fileName": filename,
                    "fileHash": "",
                    "fileSize": 0,
                    "videoDuration": 0
                }

                response = await client.post(f"{base_url}/api/v2/match", json=match_data)

                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 匹配成功: {filename}")
                    if data.get('isMatched') and data.get('matches'):
                        match = data['matches'][0]
                        print(f"   - 番剧: {match['animeTitle']}")
                        print(f"   - 分集: {match['episodeTitle']}")
                        print(f"   - 分集ID: {match['episodeId']}")
                        episode_ids.append(match['episodeId'])
                    else:
                        print(f"   - 未匹配到结果")
                else:
                    print(f"❌ 匹配失败: {filename} - HTTP {response.status_code}")

            except Exception as e:
                print(f"❌ 匹配异常: {filename} - {e}")

        # 3. 测试搜索功能
        print("\n🔍 测试搜索功能...")
        search_terms = ["鬼灭", "进击", "One Piece"]

        for term in search_terms:
            try:
                response = await client.get(f"{base_url}/api/v2/search/episodes?anime={quote(term)}")

                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 搜索成功: {term}")
                    animes = data.get('animes', [])
                    print(f"   - 找到 {len(animes)} 个结果")

                    for anime in animes[:2]:  # 只显示前2个
                        print(f"   - {anime['animeTitle']} (ID: {anime['animeId']}, 分集: {len(anime['episodes'])})")

                else:
                    print(f"❌ 搜索失败: {term} - HTTP {response.status_code}")

            except Exception as e:
                print(f"❌ 搜索异常: {term} - {e}")

        # 4. 测试弹幕获取 (使用之前匹配的分集ID)
        print("\n💬 测试弹幕获取...")
        for episode_id in episode_ids[:2]:  # 只测试前2个
            try:
                response = await client.get(f"{base_url}/api/v2/comment/{episode_id}?chConvert=1")

                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 弹幕获取成功: Episode ID {episode_id}")
                    print(f"   - 弹幕总数: {data.get('count', 0)}")

                    comments = data.get('comments', [])
                    if comments:
                        print(f"   - 弹幕示例:")
                        for j, comment in enumerate(comments[:3]):
                            p_parts = comment['p'].split(',')
                            time_sec = p_parts[0] if len(p_parts) > 0 else "0"
                            print(f"     [{j+1}] {time_sec}s: {comment['m']}")
                    else:
                        print("   - 暂无弹幕数据")

                else:
                    print(f"❌ 弹幕获取失败: Episode ID {episode_id} - HTTP {response.status_code}")

            except Exception as e:
                print(f"❌ 弹幕获取异常: Episode ID {episode_id} - {e}")

        # 5. 测试外部弹幕
        print("\n🌐 测试外部弹幕...")
        test_urls = [
            "https://example.com/video.m3u8",
            "https://www.bilibili.com/video/BV1xx411c7mu"
        ]

        for url in test_urls[:1]:  # 只测试第一个
            try:
                encoded_url = quote(url, safe='')
                response = await client.get(f"{base_url}/api/v2/extcomment?url={encoded_url}&chConvert=1")

                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 外部弹幕获取成功: {url}")
                    print(f"   - 弹幕总数: {data.get('count', 0)}")

                    comments = data.get('comments', [])
                    if comments:
                        print(f"   - 弹幕示例:")
                        for j, comment in enumerate(comments[:3]):
                            p_parts = comment['p'].split(',')
                            time_sec = p_parts[0] if len(p_parts) > 0 else "0"
                            print(f"     [{j+1}] {time_sec}s: {comment['m']}")
                    else:
                        print("   - 暂无弹幕数据")

                else:
                    print(f"❌ 外部弹幕获取失败: {url} - HTTP {response.status_code}")

            except Exception as e:
                print(f"❌ 外部弹幕获取异常: {url} - {e}")

        print("\n🎉 DanDanPlay兼容API测试完成!")

async def test_debug_endpoints():
    """测试调试接口"""
    print("\n🐛 测试调试接口...")

    base_url = "http://localhost:8080"

    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            # 测试番剧调试接口
            response = await client.get(f"{base_url}/debug/anime")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 番剧调试接口:")
                print(f"   - 番剧总数: {data.get('anime_count', 0)}")

                animes = data.get('animes', {})
                for anime_id, anime in list(animes.items())[:3]:  # 只显示前3个
                    print(f"   - ID {anime_id}: {anime.get('title', 'N/A')}")

            # 测试分集调试接口
            response = await client.get(f"{base_url}/debug/episodes")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 分集调试接口:")
                print(f"   - 分集总数: {data.get('episode_count', 0)}")

                episodes = data.get('episodes', {})
                for ep_id, episode in list(episodes.items())[:3]:  # 只显示前3个
                    print(f"   - ID {ep_id}: {episode.get('title', 'N/A')} (番剧ID: {episode.get('anime_id', 'N/A')})")

        except Exception as e:
            print(f"❌ 调试接口测试失败: {e}")

def print_usage():
    """打印使用说明"""
    print("""
🎯 弹幕聚合服务测试客户端 - DanDanPlay兼容版

使用方法:
1. 确保弹幕聚合服务已启动: ./start.sh 或 python main.py
2. 运行测试: python test_client.py

测试内容:
- ✅ 服务状态检查
- 📝 文件匹配功能 (DanDanPlay兼容)
- 🔍 搜索功能 (DanDanPlay兼容)
- 💬 弹幕获取 (DanDanPlay兼容)
- 🌐 外部弹幕获取 (DanDanPlay兼容)
- 🐛 调试接口

API接口:
- 搜索: GET /api/v2/search/episodes?anime=番剧名
- 匹配: POST /api/v2/match
- 弹幕: GET /api/v2/comment/{episode_id}
- 外部弹幕: GET /api/v2/extcomment?url=视频地址

注意事项:
- 服务默认运行在 http://localhost:8080
- 完全兼容DanDanPlay API格式
- 支持文件名自动解析和番剧匹配
- 内置内存数据库，重启后数据会丢失
    """)

if __name__ == "__main__":
    print_usage()

    # 运行测试
    asyncio.run(test_dandanplay_api())
    asyncio.run(test_debug_endpoints())
