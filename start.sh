#!/bin/bash

# 弹幕聚合服务启动脚本 - Mac M1 Pro 优化版

set -e

echo "🚀 弹幕聚合服务启动脚本 (Mac M1 Pro)"
echo "======================================="

# 检查系统架构
echo "💻 系统信息:"
echo "   架构: $(uname -m)"
echo "   系统: $(uname -s)"

# 检查Python版本
echo "📋 检查Python环境..."
if command -v python3 &> /dev/null; then
    python3 --version
else
    echo "❌ 错误: 未找到Python3，请先安装Python 3.8+"
    echo "💡 建议使用Homebrew安装: brew install python@3.11"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "main.py" ]; then
    echo "❌ 错误: 请在danmu_aggregator目录中运行此脚本"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "🔧 创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔌 激活虚拟环境..."
source venv/bin/activate

# 升级pip (M1 Mac建议)
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📦 安装依赖..."
pip install -r requirements.txt

# 显示服务信息
echo ""
echo "🎯 弹幕聚合服务 - DanDanPlay兼容版"
echo "=================================="
echo "📡 服务地址: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/docs"
echo "🔍 搜索接口: http://localhost:8080/api/v2/search/episodes?anime=番剧名"
echo "📝 匹配接口: POST http://localhost:8080/api/v2/match"
echo "💬 弹幕接口: http://localhost:8080/api/v2/comment/{episode_id}"
echo "🌐 外部弹幕: http://localhost:8080/api/v2/extcomment?url=视频地址"
echo ""
echo "⚡ 按 Ctrl+C 停止服务"
echo "🐛 调试接口: http://localhost:8080/debug/anime"
echo ""

# 启动服务
python main.py
