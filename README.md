# 弹幕聚合服务 - DanDanPlay 兼容版

完全兼容 DanDanPlay API 的弹幕聚合服务，让客户端可以像调用 DanDanPlay 一样使用。

## 🌟 核心特性

- 🎯 **完全兼容**: 100%兼容 DanDanPlay API 格式，客户端无需修改
- 🔄 **多源聚合**: 同时从 5 个弹幕源获取数据
- 📝 **智能匹配**: 自动解析文件名并创建番剧/分集数据
- 🚀 **并发请求**: 异步并发请求所有弹幕源，提高响应速度
- 🔍 **智能去重**: 基于时间和内容的弹幕去重算法
- 💾 **内存数据库**: 内置轻量级数据库，支持搜索和匹配
- 🍎 **Mac 优化**: 专为 Mac M1 Pro 优化

## 支持的弹幕源

1. `https://api.danmu.icu/?ac=dm&url=` (JSON 格式)
2. `https://dmku.hls.one/?ac=dm&url=` (JSON 格式)
3. `http://dm.apptotv.top/?ac=dm&url=` (JSON 格式)
4. `https://danmu.56uxi.com/?ac=dm&url=` (JSON 格式)
5. `https://fc.lyz05.cn/?url=` (XML 格式)

## 🚀 快速开始 (Mac M1 Pro)

### 方式 1: 使用启动脚本 (推荐)

```bash
cd danmu_aggregator
chmod +x start.sh
./start.sh
```

### 方式 2: 手动启动

```bash
cd danmu_aggregator
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python main.py
```

服务将在 `http://localhost:8080` 启动。

## 📚 DanDanPlay 兼容 API

### 1. 搜索节目和分集

```http
GET /api/v2/search/episodes?anime=鬼灭之刃&episode=1
```

### 2. 文件匹配

```http
POST /api/v2/match
Content-Type: application/json

{
  "fileName": "鬼灭之刃.S01E01.1080p.mp4",
  "fileHash": "",
  "fileSize": 0,
  "videoDuration": 0
}
```

### 3. 获取弹幕

```http
GET /api/v2/comment/1?chConvert=1&from=0&withRelated=true
```

### 4. 获取外部弹幕

```http
GET /api/v2/extcomment?url=https://example.com/video.m3u8&chConvert=1
```

**弹幕参数说明 (p 字段):**

- 格式: `时间,模式,颜色,来源`
- 时间: 弹幕出现时间(秒)
- 模式: 1=滚动, 4=底部, 5=顶部
- 颜色: RGB 颜色值
- 来源: 弹幕源标识

## 🔧 客户端使用

客户端可以完全按照 DanDanPlay API 的方式使用：

```python
# Python示例
import httpx

base_url = "http://localhost:8080"

# 1. 匹配文件
match_data = {
    "fileName": "鬼灭之刃.S01E01.mp4",
    "fileHash": "",
    "fileSize": 0,
    "videoDuration": 0
}
response = httpx.post(f"{base_url}/api/v2/match", json=match_data)
match_result = response.json()

if match_result["isMatched"]:
    episode_id = match_result["matches"][0]["episodeId"]

    # 2. 获取弹幕
    comments_response = httpx.get(f"{base_url}/api/v2/comment/{episode_id}")
    comments = comments_response.json()

    print(f"获取到 {comments['count']} 条弹幕")
```

## 🧪 测试

运行测试客户端：

```bash
python test_client.py
```

## 📊 调试接口

- 查看所有番剧: `GET /debug/anime`
- 查看所有分集: `GET /debug/episodes`
- 服务状态: `GET /`
- 健康检查: `GET /health`

## 🐳 Docker 部署

```bash
docker-compose up -d
```

## 📝 注意事项

1. **内存数据库**: 重启服务后数据会丢失
2. **缓存机制**: 弹幕数据缓存 1 小时
3. **并发限制**: 建议根据网络情况调整
4. **文件名解析**: 支持多种命名格式自动识别
5. **Mac 优化**: 针对 M1 芯片优化性能

## 🔄 工作流程

1. 客户端发送文件名到匹配接口
2. 服务解析文件名，创建番剧和分集
3. 客户端使用分集 ID 获取弹幕
4. 服务聚合多个弹幕源数据
5. 返回 DanDanPlay 格式的弹幕数据

## 🎯 与主项目的区别

- **独立运行**: 不依赖主项目的数据库
- **轻量级**: 使用内存数据库，启动快速
- **专用性**: 专门用于弹幕聚合，功能聚焦
- **兼容性**: 100%兼容 DanDanPlay API

这样客户端就可以像使用 DanDanPlay 一样使用这个服务了！
