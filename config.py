#!/usr/bin/env python3
"""
弹幕聚合服务配置文件
"""

import os
from typing import List

class Config:
    """配置类"""
    
    # 服务配置
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", 8080))
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"
    
    # 弹幕源配置
    DANMU_SOURCES: List[str] = [
        "https://api.danmu.icu/?ac=dm&url=",
        "https://dmku.hls.one/?ac=dm&url=",
        "http://dm.apptotv.top/?ac=dm&url=",
        "https://danmu.56uxi.com/?ac=dm&url=",
        "https://fc.lyz05.cn/?url="
    ]
    
    # 请求配置
    REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", 10))
    MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", 5))
    
    # 用户代理
    USER_AGENT = os.getenv("USER_AGENT", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    # 缓存配置（预留）
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "false").lower() == "true"
    CACHE_TTL = int(os.getenv("CACHE_TTL", 3600))  # 1小时
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    # 去重配置
    ENABLE_DEDUPLICATION = os.getenv("ENABLE_DEDUPLICATION", "true").lower() == "true"
    DEDUP_TIME_PRECISION = float(os.getenv("DEDUP_TIME_PRECISION", 0.01))  # 时间精度(秒)
    
    # 过滤配置
    MIN_COMMENT_LENGTH = int(os.getenv("MIN_COMMENT_LENGTH", 1))
    MAX_COMMENT_LENGTH = int(os.getenv("MAX_COMMENT_LENGTH", 500))
    
    @classmethod
    def get_danmu_sources(cls) -> List[str]:
        """获取弹幕源列表"""
        # 支持通过环境变量自定义弹幕源
        custom_sources = os.getenv("CUSTOM_DANMU_SOURCES")
        if custom_sources:
            return [s.strip() for s in custom_sources.split(",") if s.strip()]
        return cls.DANMU_SOURCES
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("📋 当前配置:")
        print(f"   - 服务地址: {cls.HOST}:{cls.PORT}")
        print(f"   - 调试模式: {cls.DEBUG}")
        print(f"   - 请求超时: {cls.REQUEST_TIMEOUT}s")
        print(f"   - 最大并发: {cls.MAX_CONCURRENT_REQUESTS}")
        print(f"   - 弹幕源数量: {len(cls.get_danmu_sources())}")
        print(f"   - 启用去重: {cls.ENABLE_DEDUPLICATION}")
        print(f"   - 启用缓存: {cls.ENABLE_CACHE}")

# 环境变量示例
ENV_EXAMPLE = """
# 弹幕聚合服务环境变量配置示例

# 服务配置
HOST=0.0.0.0
PORT=8080
DEBUG=false

# 请求配置
REQUEST_TIMEOUT=10
MAX_CONCURRENT_REQUESTS=5
USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# 自定义弹幕源（可选）
# CUSTOM_DANMU_SOURCES="https://api1.com/?url=,https://api2.com/?url="

# 缓存配置
ENABLE_CACHE=false
CACHE_TTL=3600

# 日志级别
LOG_LEVEL=INFO

# 去重配置
ENABLE_DEDUPLICATION=true
DEDUP_TIME_PRECISION=0.01

# 过滤配置
MIN_COMMENT_LENGTH=1
MAX_COMMENT_LENGTH=500
"""

if __name__ == "__main__":
    Config.print_config()
    print("\n" + "="*50)
    print("环境变量配置示例:")
    print(ENV_EXAMPLE)
