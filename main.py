#!/usr/bin/env python3
"""
弹幕聚合服务 - 完全兼容dandanplay API
将多个弹幕接口的数据转换为dandanplay兼容格式
"""

import asyncio
import json
import logging
import re
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional
from urllib.parse import quote, unquote, urlparse
from datetime import datetime
import hashlib

import httpx
from fastapi import FastAPI, Query, HTTPException, Path, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="弹幕聚合服务",
    description="完全兼容dandanplay API的弹幕聚合服务",
    version="1.0.0"
)

# 弹幕源配置
DANMU_SOURCES = [
    "https://api.danmu.icu/?ac=dm&url=",
    "https://dmku.hls.one/?ac=dm&url=",
    "http://dm.apptotv.top/?ac=dm&url=",
    "https://danmu.56uxi.com/?ac=dm&url=",
    "https://fc.lyz05.cn/?url="
]

# ===== DanDanPlay 兼容模型 =====

class DandanResponseBase(BaseModel):
    """dandanplay API基础响应模型"""
    success: bool = True
    errorCode: int = 0
    errorMessage: str = ""

class Comment(BaseModel):
    """弹幕模型"""
    cid: int
    p: str  # 格式: "时间,模式,颜色,来源"
    m: str  # 弹幕内容

class CommentResponse(DandanResponseBase):
    """弹幕响应模型"""
    count: int
    comments: List[Comment]

class EpisodeInfo(BaseModel):
    """分集信息模型"""
    episodeId: int
    episodeTitle: str

class AnimeInfo(BaseModel):
    """番剧信息模型"""
    animeId: int
    animeTitle: str
    imageUrl: str = ""
    searchKeyword: str = ""
    type: str = "other"
    typeDescription: str = "其他"
    isOnAir: bool = False
    airDay: int = 0
    isFavorited: bool = False
    rating: float = 0.0
    episodes: List[EpisodeInfo]

class SearchEpisodesResponse(DandanResponseBase):
    """搜索分集响应模型"""
    animes: List[AnimeInfo]

class MatchInfo(BaseModel):
    """匹配信息模型"""
    episodeId: int
    animeId: int
    animeTitle: str
    episodeTitle: str
    type: str
    typeDescription: str
    shift: float = 0.0

class MatchResponse(DandanResponseBase):
    """匹配响应模型"""
    isMatched: bool
    matches: List[MatchInfo] = []

class BatchMatchRequestItem(BaseModel):
    """批量匹配请求项"""
    fileName: str
    fileHash: str = ""
    fileSize: int = 0
    videoDuration: int = 0

class BatchMatchRequest(BaseModel):
    """批量匹配请求"""
    requests: List[BatchMatchRequestItem]

def parse_json_danmu(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    解析JSON格式弹幕数据
    格式: [时间, 位置, 颜色, "", 内容, "", "", 字体大小]
    """
    comments = []
    if not isinstance(data, dict) or 'danmuku' not in data:
        return comments
    
    danmuku_list = data.get('danmuku', [])
    for i, item in enumerate(danmuku_list):
        if not isinstance(item, list) or len(item) < 5:
            continue
            
        try:
            time_sec = float(item[0])
            position = item[1]  # "top", "right", "bottom"
            color_hex = item[2]  # "#ff00d0"
            content = item[4]
            
            # 转换位置为dandanplay格式
            mode = 1  # 默认滚动
            if position == "top":
                mode = 5  # 顶部
            elif position == "bottom":
                mode = 4  # 底部
            elif position == "right":
                mode = 1  # 滚动
                
            # 转换颜色为RGB数值
            color_rgb = 16777215  # 默认白色
            if color_hex and color_hex.startswith('#'):
                try:
                    color_rgb = int(color_hex[1:], 16)
                except ValueError:
                    pass
            
            comments.append({
                'cid': i + 1,
                'time': time_sec,
                'mode': mode,
                'color': color_rgb,
                'content': content,
                'source': 1
            })
        except (ValueError, IndexError) as e:
            logger.warning(f"解析JSON弹幕项失败: {item}, 错误: {e}")
            continue
    
    return comments

def parse_xml_danmu(xml_content: str) -> List[Dict[str, Any]]:
    """
    解析XML格式弹幕数据
    格式: <d p="时间,模式,字体大小,颜色,时间戳,池,用户ID,弹幕ID,权重">内容</d>
    """
    comments = []
    try:
        root = ET.fromstring(xml_content)
        for i, d_elem in enumerate(root.findall('d')):
            p_attr = d_elem.get('p', '')
            content = d_elem.text or ''
            
            if not p_attr or not content:
                continue
                
            try:
                p_parts = p_attr.split(',')
                if len(p_parts) < 4:
                    continue
                    
                time_sec = float(p_parts[0])
                mode = int(p_parts[1])
                font_size = int(p_parts[2]) if len(p_parts) > 2 else 25
                color = int(p_parts[3]) if len(p_parts) > 3 else 16777215
                
                comments.append({
                    'cid': i + 1,
                    'time': time_sec,
                    'mode': mode,
                    'color': color,
                    'content': content,
                    'source': 2
                })
            except (ValueError, IndexError) as e:
                logger.warning(f"解析XML弹幕项失败: p='{p_attr}', content='{content}', 错误: {e}")
                continue
                
    except ET.ParseError as e:
        logger.error(f"XML解析失败: {e}")
    
    return comments

async def fetch_danmu_from_source(client: httpx.AsyncClient, source_url: str, video_url: str) -> List[Dict[str, Any]]:
    """从单个弹幕源获取弹幕数据"""
    try:
        full_url = source_url + quote(video_url, safe=':/?#[]@!$&\'()*+,;=')
        logger.info(f"请求弹幕源: {full_url}")
        
        response = await client.get(full_url, timeout=10.0)
        response.raise_for_status()
        
        content_type = response.headers.get('content-type', '').lower()
        
        if 'application/json' in content_type or 'text/json' in content_type:
            # JSON格式
            try:
                data = response.json()
                return parse_json_danmu(data)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                return []
        elif 'application/xml' in content_type or 'text/xml' in content_type or response.text.strip().startswith('<?xml'):
            # XML格式
            return parse_xml_danmu(response.text)
        else:
            # 尝试自动检测格式
            text = response.text.strip()
            if text.startswith('<?xml') or text.startswith('<'):
                return parse_xml_danmu(text)
            elif text.startswith('{') or text.startswith('['):
                try:
                    data = json.loads(text)
                    return parse_json_danmu(data)
                except json.JSONDecodeError:
                    pass
            
            logger.warning(f"未知的响应格式: {content_type}")
            return []
            
    except httpx.TimeoutException:
        logger.warning(f"请求超时: {source_url}")
        return []
    except httpx.HTTPStatusError as e:
        logger.warning(f"HTTP错误 {e.response.status_code}: {source_url}")
        return []
    except Exception as e:
        logger.error(f"获取弹幕失败: {source_url}, 错误: {e}")
        return []

# ===== 内存数据库 =====
# 模拟数据库存储番剧和分集信息
anime_database = {}
episode_database = {}
next_anime_id = 1
next_episode_id = 1

def get_or_create_anime(title: str, video_url: str) -> Dict[str, Any]:
    """根据标题获取或创建番剧"""
    global next_anime_id

    # 简单的标题匹配
    for anime_id, anime in anime_database.items():
        if anime['title'].lower() == title.lower():
            return anime

    # 创建新番剧
    anime_id = next_anime_id
    next_anime_id += 1

    anime = {
        'id': anime_id,
        'title': title,
        'type': 'other',
        'episodes': [],
        'created_from_url': video_url
    }
    anime_database[anime_id] = anime
    return anime

def get_or_create_episode(anime_id: int, episode_title: str, video_url: str) -> Dict[str, Any]:
    """获取或创建分集"""
    global next_episode_id

    # 检查是否已存在
    for episode_id, episode in episode_database.items():
        if episode['anime_id'] == anime_id and episode['video_url'] == video_url:
            return episode

    # 创建新分集
    episode_id = next_episode_id
    next_episode_id += 1

    episode = {
        'id': episode_id,
        'anime_id': anime_id,
        'title': episode_title,
        'video_url': video_url,
        'comments_cache': None,
        'cache_time': None
    }
    episode_database[episode_id] = episode

    # 添加到番剧的分集列表
    if anime_id in anime_database:
        anime_database[anime_id]['episodes'].append(episode_id)

    return episode

def parse_filename_for_match(filename: str) -> Dict[str, Any]:
    """解析文件名获取番剧和分集信息"""
    # 移除文件扩展名
    name = re.sub(r'\.[^.]+$', '', filename)

    # 尝试匹配常见的命名模式
    patterns = [
        r'(.+?)[\s\.\-_]*[Ss](\d+)[Ee](\d+)',  # S01E01格式
        r'(.+?)[\s\.\-_]*第(\d+)季[\s\.\-_]*第(\d+)[集话]',  # 中文格式
        r'(.+?)[\s\.\-_]*(\d+)[\s\.\-_]*(\d+)',  # 简单数字格式
        r'(.+)',  # 只有标题
    ]

    for pattern in patterns:
        match = re.match(pattern, name, re.IGNORECASE)
        if match:
            groups = match.groups()
            title = groups[0].strip()

            if len(groups) >= 3:
                season = int(groups[1])
                episode = int(groups[2])
                episode_title = f"第{episode}集"
            elif len(groups) == 2:
                episode = int(groups[1])
                episode_title = f"第{episode}集"
            else:
                episode_title = "第1集"

            return {
                'title': title,
                'episode_title': episode_title,
                'original_filename': filename
            }

    return {
        'title': filename,
        'episode_title': '第1集',
        'original_filename': filename
    }

async def aggregate_danmu(video_url: str) -> List[Dict[str, Any]]:
    """聚合所有弹幕源的数据"""
    all_comments = []

    async with httpx.AsyncClient(
        headers={'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'},
        follow_redirects=True,
        timeout=10.0
    ) as client:
        # 并发请求所有弹幕源
        tasks = [
            fetch_danmu_from_source(client, source, video_url)
            for source in DANMU_SOURCES
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"弹幕源 {i+1} 请求异常: {result}")
                continue
            elif isinstance(result, list):
                logger.info(f"弹幕源 {i+1} 获取到 {len(result)} 条弹幕")
                all_comments.extend(result)

    # 去重和排序
    unique_comments = {}
    for comment in all_comments:
        # 使用时间+内容作为去重键
        key = f"{comment['time']:.2f}_{comment['content']}"
        if key not in unique_comments:
            unique_comments[key] = comment

    # 按时间排序
    sorted_comments = sorted(unique_comments.values(), key=lambda x: x['time'])

    logger.info(f"聚合完成: 总计 {len(all_comments)} 条弹幕，去重后 {len(sorted_comments)} 条")
    return sorted_comments

# ===== DanDanPlay 兼容API接口 =====

@app.get("/", summary="服务状态")
async def root():
    """服务状态检查"""
    return {
        "service": "弹幕聚合服务 - DanDanPlay兼容",
        "version": "1.0.0",
        "status": "running",
        "sources": len(DANMU_SOURCES),
        "anime_count": len(anime_database),
        "episode_count": len(episode_database)
    }

@app.get("/api/v2/search/episodes", response_model=SearchEpisodesResponse, summary="[dandanplay兼容] 搜索节目和分集")
async def search_episodes(
    anime: str = Query(..., description="节目名称"),
    episode: Optional[str] = Query(None, description="分集标题")
):
    """
    搜索节目和分集 - 兼容dandanplay API
    """
    try:
        results = []
        search_term = anime.lower()

        # 在内存数据库中搜索
        for anime_id, anime_data in anime_database.items():
            if search_term in anime_data['title'].lower():
                episodes = []
                for ep_id in anime_data['episodes']:
                    if ep_id in episode_database:
                        ep = episode_database[ep_id]
                        episodes.append(EpisodeInfo(
                            episodeId=ep['id'],
                            episodeTitle=ep['title']
                        ))

                results.append(AnimeInfo(
                    animeId=anime_data['id'],
                    animeTitle=anime_data['title'],
                    type=anime_data['type'],
                    typeDescription="其他",
                    episodes=episodes
                ))

        return SearchEpisodesResponse(animes=results)

    except Exception as e:
        logger.error(f"搜索失败: {e}", exc_info=True)
        return SearchEpisodesResponse(
            success=False,
            errorCode=500,
            errorMessage=f"搜索失败: {str(e)}",
            animes=[]
        )

@app.post("/api/v2/match", response_model=MatchResponse, summary="[dandanplay兼容] 文件匹配")
async def match_file(request: BatchMatchRequestItem):
    """
    文件匹配 - 兼容dandanplay API
    根据文件名创建或匹配番剧和分集
    """
    try:
        # 解析文件名
        parsed = parse_filename_for_match(request.fileName)

        # 创建或获取番剧
        anime = get_or_create_anime(parsed['title'], request.fileName)

        # 创建或获取分集
        episode = get_or_create_episode(anime['id'], parsed['episode_title'], request.fileName)

        # 返回匹配结果
        match_info = MatchInfo(
            episodeId=episode['id'],
            animeId=anime['id'],
            animeTitle=anime['title'],
            episodeTitle=episode['title'],
            type=anime['type'],
            typeDescription="其他"
        )

        return MatchResponse(
            isMatched=True,
            matches=[match_info]
        )

    except Exception as e:
        logger.error(f"文件匹配失败: {e}", exc_info=True)
        return MatchResponse(
            success=False,
            errorCode=500,
            errorMessage=f"匹配失败: {str(e)}",
            isMatched=False,
            matches=[]
        )

@app.get("/api/v2/comment/{episode_id}", response_model=CommentResponse, summary="[dandanplay兼容] 获取弹幕")
async def get_comments(
    episode_id: int = Path(..., description="分集ID"),
    chConvert: int = Query(0, description="简繁转换: 0-不转换, 1-转简体, 2-转繁体"),
    from_time: int = Query(0, alias="from", description="弹幕开始时间(秒)"),
    with_related: bool = Query(True, alias="withRelated", description="是否包含关联弹幕")
):
    """
    获取弹幕 - 兼容dandanplay API
    """
    try:
        # 查找分集
        if episode_id not in episode_database:
            return CommentResponse(
                success=False,
                errorCode=404,
                errorMessage="分集不存在",
                count=0,
                comments=[]
            )

        episode = episode_database[episode_id]

        # 检查缓存
        if episode['comments_cache'] and episode['cache_time']:
            cache_age = (datetime.now() - episode['cache_time']).total_seconds()
            if cache_age < 3600:  # 1小时缓存
                logger.info(f"使用缓存的弹幕数据: episode_id={episode_id}")
                return episode['comments_cache']

        # 获取弹幕数据
        video_url = episode['video_url']
        comments_data = await aggregate_danmu(video_url)

        # 转换为dandanplay格式
        dandan_comments = []
        for i, comment in enumerate(comments_data):
            # 过滤开始时间
            if comment['time'] < from_time:
                continue

            p = f"{comment['time']},{comment['mode']},{comment['color']},{comment['source']}"
            dandan_comments.append(Comment(
                cid=i + 1,
                p=p,
                m=comment['content']
            ))

        response = CommentResponse(
            count=len(dandan_comments),
            comments=dandan_comments
        )

        # 缓存结果
        episode['comments_cache'] = response
        episode['cache_time'] = datetime.now()

        logger.info(f"返回 {len(dandan_comments)} 条弹幕: episode_id={episode_id}")
        return response

    except Exception as e:
        logger.error(f"获取弹幕失败: {e}", exc_info=True)
        return CommentResponse(
            success=False,
            errorCode=500,
            errorMessage=f"获取弹幕失败: {str(e)}",
            count=0,
            comments=[]
        )

@app.get("/api/v2/extcomment", response_model=CommentResponse, summary="[dandanplay兼容] 获取外部弹幕")
async def get_external_comments(
    url: str = Query(..., description="外部视频链接"),
    chConvert: int = Query(0, description="简繁转换: 0-不转换, 1-转简体, 2-转繁体")
):
    """
    获取外部弹幕 - 兼容dandanplay API
    直接从URL获取弹幕，不需要先匹配
    """
    try:
        # 直接聚合弹幕数据
        comments_data = await aggregate_danmu(url)

        # 转换为dandanplay格式
        dandan_comments = []
        for i, comment in enumerate(comments_data):
            p = f"{comment['time']},{comment['mode']},{comment['color']},{comment['source']}"
            dandan_comments.append(Comment(
                cid=i + 1,
                p=p,
                m=comment['content']
            ))

        response = CommentResponse(
            count=len(dandan_comments),
            comments=dandan_comments
        )

        logger.info(f"外部弹幕获取成功: {len(dandan_comments)} 条弹幕")
        return response

    except Exception as e:
        logger.error(f"获取外部弹幕失败: {e}", exc_info=True)
        return CommentResponse(
            success=False,
            errorCode=500,
            errorMessage=f"获取外部弹幕失败: {str(e)}",
            count=0,
            comments=[]
        )

@app.get("/health", summary="健康检查")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/debug/anime", summary="调试: 查看所有番剧")
async def debug_anime():
    """调试接口: 查看内存中的所有番剧"""
    return {
        "anime_count": len(anime_database),
        "animes": anime_database
    }

@app.get("/debug/episodes", summary="调试: 查看所有分集")
async def debug_episodes():
    """调试接口: 查看内存中的所有分集"""
    return {
        "episode_count": len(episode_database),
        "episodes": {k: {**v, 'comments_cache': None} for k, v in episode_database.items()}  # 不显示缓存内容
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动弹幕聚合服务 - DanDanPlay兼容版")
    print("📡 API文档: http://localhost:8080/docs")
    print("🔍 搜索接口: http://localhost:8080/api/v2/search/episodes?anime=番剧名")
    print("📝 匹配接口: POST http://localhost:8080/api/v2/match")
    print("💬 弹幕接口: http://localhost:8080/api/v2/comment/{episode_id}")
    print("🌐 外部弹幕: http://localhost:8080/api/v2/extcomment?url=视频地址")
    uvicorn.run(app, host="0.0.0.0", port=8080)
