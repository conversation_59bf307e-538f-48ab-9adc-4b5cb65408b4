version: '3.8'

services:
  danmu-aggregator:
    build: .
    container_name: danmu-aggregator
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - PYTHONUNBUFFERED=1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - danmu-network

networks:
  danmu-network:
    driver: bridge
